<template>
  <div class="app-container">
    <el-card class="search-form">
      <el-form :inline="true" :model="search" :rules="rules" ref="searchForm" class="search-form" label-width="100px" size="small">
        <el-row :gutter="24">
          <el-col :span="10">
            <el-form-item label="起始日期" prop="startTime">
              <el-date-picker
                style="width: 200px;"
                v-model="search.startTime"
                type="date"
                value-format="yyyyMMdd"
                placeholder="请选择起始日期"
                clearable
                @change="validateDateRange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="终止日期" prop="endTime">
              <el-date-picker
                style="width: 200px;"
                v-model="search.endTime"
                type="date"
                value-format="yyyyMMdd"
                placeholder="请选择终止日期"
                clearable
                @change="validateDateRange"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" class="search-btns">
            <el-button type="primary" icon="el-icon-search" @click="handleQuery">查询</el-button>
            <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
            <el-button type="success" icon="el-icon-download" @click="handleExport" :disabled="!data.length">导出</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 数据为空时的提示信息 -->
    <el-alert
      v-if="!data.length && !loading && !hasSearched"
      title="请设置查询条件后点击查询按钮获取数据"
      type="info"
      :closable="false"
      show-icon
      style="margin-bottom: 20px;"
    />

    <!-- 查询结果表格 -->
    <yo-table
      v-loading="loading"
      :option="option"
      :data="data"
      ref="crud"
      :page.sync="page"
      @on-load="onLoad"
      @search-change="searchChange"
      @refresh-change="refresh"
      v-model="formParent"
    />
  </div>
</template>

<script>
import { queryTxnLogDetail, exportTxnLogDetail } from '@/api/xzp/inttxnlog'
import { formatDate } from '@/utils'

export default {
  name: 'TxnLogDetail',
  data() {
    return {
      loading: false,
      hasSearched: false,
      data: [],
      formParent: {},
      search: {
        startTime: '',
        endTime: ''
      },
      rules: {
        startTime: [
          { required: true, message: '请选择起始日期', trigger: 'change' }
        ],
        endTime: [
          { required: true, message: '请选择终止日期', trigger: 'change' }
        ]
      },
      page: {
        pageSize: 20,
        pageNum: 1
      },
      option: {
        index: true,
        indexLabel: '序号',
        addBtn: false,
        editBtn: false,
        delBtn: false,
        selection: false,
        card: true,
        align: 'center',
        menuAlign: 'center',
        searchBtn: false,
        refreshBtn: false,
        emptyBtn: false,
        tip: false,
        columnBtn: false,
        menu: false,
        showSummary: true,
        summaryMethod: this.getSummaries,
        viewTitle: '缴费明细查询',
        rowClassName: this.getRowClassName,
        column: [
          {
            prop: 'setdate',
            label: '银联清算日期',
            width: 120,
            formatter: (row, column, cellValue) => {
              // 小计行不显示日期
              return row.str === '小计' ? '' : cellValue
            }
          },
          {
            prop: 'str',
            label: '业务代码',
            width: 200,
            formatter: (row, column, cellValue) => {
              return cellValue
            },
            className: (row) => {
              return row.str === '小计' ? 'subtotal-row' : ''
            }
          },
          {
            prop: 'merchid',
            label: '委托代码',
            width: 120,
            formatter: (row, column, cellValue) => {
              // 小计行不显示委托代码
              return row.str === '小计' ? '' : cellValue
            }
          },
          {
            prop: 'str30',
            label: '子业务代码',
            width: 120,
            formatter: (row, column, cellValue) => {
              // 小计行不显示子业务代码
              return row.str === '小计' ? '' : cellValue
            }
          },
          {
            prop: 'str31',
            label: '子业务',
            width: 120,
            formatter: (row, column, cellValue) => {
              // 小计行不显示子业务
              return row.str === '小计' ? '' : cellValue
            }
          },
          {
            prop: 'num',
            label: '应付笔数',
            width: 100,
            formatter: (row, column, cellValue) => {
              return cellValue ? Number(cellValue).toLocaleString() : '0'
            },
            className: (row) => {
              return row.str === '小计' ? 'subtotal-row' : ''
            }
          },
          {
            prop: 'amt',
            label: '应付金额(元)',
            width: 150,
            formatter: (row, column, cellValue) => {
              return cellValue ? (Number(cellValue) / 100).toFixed(2) : '0.00'
            },
            className: (row) => {
              return row.str === '小计' ? 'subtotal-row' : ''
            }
          },
          {
            prop: 'receivableAmt',
            label: '应收金额(元)',
            width: 150,
            formatter: (row, column, cellValue) => {
              return '0.00'
            },
            className: (row) => {
              return row.str === '小计' ? 'subtotal-row' : ''
            }
          },
          {
            prop: 'netAmt',
            label: '轧差金额(元)',
            width: 150,
            formatter: (row, column, cellValue, index) => {
              const receivable = 0.00
              const payable = row.amt ? (Number(row.amt) / 100) : 0
              const net = receivable - payable
              return net.toFixed(2)
            },
            className: (row) => {
              return row.str === '小计' ? 'subtotal-row' : ''
            }
          }
        ]
      }
    }
  },
  methods: {
    // 获取行样式类名
    getRowClassName({ row, rowIndex }) {
      return row.str === '小计' ? 'subtotal-row-class' : ''
    },

    // yo-table加载数据方法
    onLoad() {
      this.getList()
    },
    
    // yo-table搜索变化方法
    searchChange(params, done) {
      this.search = { ...this.search, ...params }
      this.getList()
      done()
    },
    
    // yo-table刷新方法
    refresh() {
      this.getList()
    },

    // 计算总计
    getSummaries(param) {
      const { columns, data } = param
      const sums = []

      // 过滤掉小计行，只计算明细数据的总计
      const detailData = data.filter(item => item.str !== '小计')

      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '总计'
          return
        }

        if (column.property === 'num') {
          // 应付笔数总计
          const sum = detailData.reduce((prev, curr) => {
            const value = Number(curr.num)
            if (!isNaN(value)) {
              return prev + value
            } else {
              return prev
            }
          }, 0)
          sums[index] = sum.toLocaleString()
        } else if (column.property === 'amt') {
          // 应付金额总计
          const sum = detailData.reduce((prev, curr) => {
            const value = Number(curr.amt)
            if (!isNaN(value)) {
              return prev + value
            } else {
              return prev
            }
          }, 0)
          sums[index] = (sum / 100).toFixed(2)
        } else if (column.property === 'receivableAmt') {
          // 应收金额总计（固定为0.00）
          sums[index] = '0.00'
        } else if (column.property === 'netAmt') {
          // 轧差金额总计（应收总计 - 应付总计）
          const payableSum = detailData.reduce((prev, curr) => {
            const value = Number(curr.amt)
            if (!isNaN(value)) {
              return prev + value
            } else {
              return prev
            }
          }, 0)
          const receivableSum = 0 // 应收金额固定为0
          const netSum = receivableSum - (payableSum / 100)
          sums[index] = netSum.toFixed(2)
        } else {
          sums[index] = ''
        }
      })
      return sums
    },
    
    // 查询数据
    handleQuery() {
      this.$refs.searchForm.validate(valid => {
        if (valid) {
          this.page.pageNum = 1
          this.getList()
        }
      })
    },
    
    // 获取列表数据
    async getList() {
      this.loading = true
      this.hasSearched = true
      try {
        const response = await queryTxnLogDetail(
          this.search,
          this.page.pageNum,
          this.page.pageSize
        )
        console.log('API响应数据:', response)
        if (response.code === '0') {
          // 检查数据结构
          if (response.data && response.data.list) {
            this.data = response.data.list
            this.page.total = response.data.total || 0
          } else if (Array.isArray(response.data)) {
            // 如果直接返回数组
            this.data = response.data
            this.page.total = response.data.length
          } else {
            console.warn('数据结构异常:', response.data)
            this.data = []
            this.page.total = 0
          }
          console.log('处理后的数据:', this.data)
        } else {
          this.$message.error(response.message || '查询失败')
          this.data = []
          this.page.total = 0
        }
      } catch (error) {
        console.error('查询缴费明细失败:', error)
        this.$message.error('查询失败，请稍后重试')
        this.data = []
        this.page.total = 0
      } finally {
        this.loading = false
      }
    },
    
    // 重置查询
    resetQuery() {
      this.$refs.searchForm.resetFields()
      this.search = {
        startTime: '',
        endTime: ''
      }
      this.data = []
      this.page.total = 0
      this.page.pageNum = 1
      this.hasSearched = false
    },
    
    // 日期范围验证
    validateDateRange() {
      if (this.search.startTime && this.search.endTime) {
        const start = new Date(this.search.startTime.replace(/(\d{4})(\d{2})(\d{2})/, '$1-$2-$3'))
        const end = new Date(this.search.endTime.replace(/(\d{4})(\d{2})(\d{2})/, '$1-$2-$3'))

        if (start > end) {
          this.$message.warning('起始日期不能大于终止日期')
          this.search.endTime = ''
          return
        }
      }
    },
    
    // 导出功能
    handleExport() {
      if (!this.data.length) {
        this.$message.warning('没有可导出的数据')
        return
      }
      
      this.$confirm('确认导出缴费明细数据？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.exportData()
      })
    },
    
    // 执行导出
    async exportData() {
      try {
        const params = {
          ...this.search
        }

        // 调用后端导出接口
        const response = await exportTxnLogDetail(params)
        this.handleExportData(response)
      } catch (error) {
        console.error('导出失败:', error)
        this.$message.error('导出失败，请稍后重试')
      }
    },

    // 处理返回的流文件
    handleExportData(res) {
      if (!res) return
      let data = res.data
      let filename = '缴费明细列表.xls'

      // 尝试从响应头获取文件名
      if (res.headers && res.headers['content-disposition']) {
        const disposition = res.headers['content-disposition']
        const filenameMatch = disposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/)
        if (filenameMatch && filenameMatch[1]) {
          filename = decodeURI(filenameMatch[1].replace(/['"]/g, ''))
        }
      } else {
        // 如果没有从响应头获取到文件名，使用默认格式
        filename = `缴费明细_${this.search.startTime}_${this.search.endTime}_${formatDate(new Date(), 'yyyyMMddHHmmss')}.xls`
      }

      const link = document.createElement('a')
      // 创建 Blob对象 存储二进制文件
      let blob = new Blob([data], { type: 'application/x-excel' })
      link.style.display = 'none'
      link.href = URL.createObjectURL(blob)
      link.download = filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      this.$message.success('导出成功')
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.search-form {
  margin-bottom: 20px;

  .el-form {
    .el-row {
      margin-bottom: 10px;
    }

    .search-btns {
      text-align: center;
      margin-top: 10px;
    }
  }
}

.custom-input {
  width: 100%;
}

.total-info {
  float: right;
  color: #909399;
  font-size: 14px;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}

// 小计行样式
:deep(.subtotal-row) {
  background-color: #f5f7fa !important;
  font-weight: bold !important;
  color: #409eff !important;
}

// 小计行的单元格样式
:deep(.el-table__row .subtotal-row) {
  background-color: #f5f7fa !important;
  font-weight: bold !important;
  color: #409eff !important;
}

// 整行小计样式
:deep(.el-table__row) {
  &.subtotal-row-class {
    background-color: #f5f7fa !important;

    .cell {
      font-weight: bold !important;
      color: #409eff !important;
    }
  }
}
</style>