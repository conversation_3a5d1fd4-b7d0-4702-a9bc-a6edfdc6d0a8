# 分组小计优化说明

## 优化目标
优化缴费明细查询中的分组小计显示，使小计行出现在每组的最后一行，并在业务代码列显示"小计"。

## 问题分析
原始实现中，小计行通过SQL的`GROUPING SETS`和`ROLLUP`生成，但小计行的位置不固定，可能出现在组中间，影响数据的可读性。

## 优化方案

### 1. 后端SQL优化
**文件**: `xm-xzp-impl\src\main\resources\mapper\IntTxnLogMapper.xml`

**主要修改**:
- 在原有的`GROUPING SETS`查询基础上，添加了`is_subtotal`字段来标识小计行
- 优化了`ORDER BY`子句，确保小计行排在每组的最后：
  ```sql
  order by q.local_dt, concat(q.ope_cd, '_', q.merch_id), is_subtotal, q.pay_id
  ```

**关键改进**:
- 保持了原有的分组逻辑和计算准确性
- 通过排序确保小计行位置正确
- 添加了`is_subtotal`标识字段便于前端处理

### 2. 前端显示优化
**文件**: `src\views\xzp\txnlogdetail\index.vue`

**主要修改**:

#### 2.1 表格列配置优化
- 为小计行添加特殊的格式化处理
- 小计行在非关键列显示为空，突出重点信息
- 添加了`className`属性为小计行设置特殊样式

#### 2.2 行样式配置
- 添加了`rowClassName`方法，为小计行添加特殊CSS类
- 小计行使用`subtotal-row-class`样式类

#### 2.3 总计计算优化
- 修改了`getSummaries`方法，过滤掉小计行避免重复计算
- 确保总计只计算明细数据，不包含小计行

#### 2.4 样式美化
添加了小计行的特殊样式：
```scss
:deep(.el-table__row) {
  &.subtotal-row-class {
    background-color: #f5f7fa !important;
    
    .cell {
      font-weight: bold !important;
      color: #409eff !important;
    }
  }
}
```

## 优化效果

### 显示效果
1. **小计行位置**: 小计行现在固定出现在每组的最后一行
2. **视觉突出**: 小计行使用特殊的背景色和字体样式，便于识别
3. **信息清晰**: 小计行只在关键列（业务代码、笔数、金额）显示信息，其他列为空

### 数据准确性
1. **计算正确**: 保持了原有的分组计算逻辑，确保小计数据准确
2. **总计正确**: 总计行不会重复计算小计行的数据
3. **排序合理**: 数据按日期、业务分组、小计标识进行排序

## 技术要点

### SQL优化技术
- 使用`GROUPING SETS`和`ROLLUP`进行分组汇总
- 通过`CASE WHEN`语句生成标识字段
- 使用复合排序确保数据顺序

### 前端优化技术
- Vue组件的动态样式绑定
- Element UI表格的自定义格式化
- CSS深度选择器的使用

## 测试建议
1. 验证小计行位置是否正确（每组最后一行）
2. 验证小计数据计算是否准确
3. 验证总计行计算是否正确（不包含小计行）
4. 验证样式显示是否符合预期
5. 测试不同日期范围的数据显示

## 兼容性说明
- 保持了原有的API接口不变
- 前端组件向后兼容
- 数据结构保持一致，只是显示顺序和样式的优化
